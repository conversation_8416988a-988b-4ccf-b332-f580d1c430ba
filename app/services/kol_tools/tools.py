#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project ：KOL-python
@File    ：tools
<AUTHOR>
@Date    ：2025/4/9 下午4:39 
'''
import json
from sqlalchemy.orm import Session
from typing import Dict, List, Optional, Tuple

from app.crud import filter_data, kol_info, video_info, filter_kol_association
from app.services.creable_crawler_service import CreableService
from app.services.modash_crawler_service import ModashWebCrawler
from app.services.ttone_crawler_service import ttone_search
from app.services.tiktok_crawler_service import TikTokAPIClient
from app.services.instagram_service import InstagramScraperService
from app.services.feishu_lark_service import Lark
from app.services.llm_service import email_and_keywords_from_bio
from app.services.tools import compute_view_mean_and_median_k
from app.logging_config import get_task_logger
from app.core.config import settings

# 使用任务日志记录器
logger = get_task_logger("app.worker.tasks.kol_data")


def _validate_params(source: str, platform: str) -> None:
    """
    验证任务参数

    Args:
        source: 数据源
        platform: 平台

    Raises:
        ValueError: 当参数无效时
    """
    if not source:
        logger.error("缺少必要参数: source")
        raise ValueError("缺少必要参数: source")

    if source not in ["creable", "modash", "ttone"]:
        logger.error(f"不支持的数据源: {source}")
        raise ValueError(f"不支持的数据源: {source}")

    if platform not in ["tiktok", "instagram", "youtube"]:
        logger.error(f"不支持的平台: {platform}")
        raise ValueError(f"不支持的平台: {platform}")


def _collect_source_data(source: str, query_params: Dict) -> List[Dict]:
    """
    根据数据源采集原始数据

    Args:
        source: 数据源名称（creable/modash/ttone）
        query_params: 查询参数

    Returns:
        原始数据列表
    """
    logger.info(f"开始从数据源 {source} 采集原始数据")

    try:
        cookie = query_params.get("cookie", "")
        filter_body = query_params.get("filter_body", "{}")

        # 尝试将filter_body解析为JSON对象
        try:
            filter_body_dict = json.loads(filter_body) if isinstance(filter_body, str) else filter_body
        except Exception as e:
            logger.error(f"解析filter_body失败: {str(e)}")
            filter_body_dict = {}

        if source == "creable":
            # 使用CreableService类
            token = cookie  # Creable使用token进行认证
            creable_service = CreableService(token=token, logger=logger)
            return creable_service.search_creators(filter_data=filter_body_dict)

        elif source == "modash":
            # 使用ModashWebCrawler类
            platform = query_params.get("platform", "").lower()
            if not platform:
                logger.error("Modash请求缺少platform参数")
                return []

            modash_crawler = ModashWebCrawler(
                cookie=cookie,
                request_body=filter_body_dict,
                platform=platform
            )
            return modash_crawler.batch_search_modash_kol_info()

        elif source == "ttone":
            # 使用TiktokOneClient类或ttone_search函数
            return ttone_search(
                cookie=cookie,
                request_body=filter_body_dict
            )

        else:
            logger.error(f"不支持的数据源: {source}")
            return []

    except Exception as e:
        logger.exception(f"从数据源 {source} 采集数据时发生错误: {str(e)}")
        return []


def _standardize_data(raw_data: List[Dict], source: str, platform: str) -> List[Dict]:
    """
    数据标准化处理

    Args:
        raw_data: 原始数据列表
        source: 数据源名称（creable/modash/ttone）
        platform: 平台名称（TikTok/Instagram/YouTube）

    Returns:
        标准化后的数据列表
    """
    logger.info(f"开始对来自 {source} 的数据进行标准化处理")

    standardized_list = []
    platform_prefix = ""

    # 设置平台前缀
    if platform.upper() == "TIKTOK":
        platform_prefix = "TK_"
    elif platform.upper() == "INSTAGRAM":
        platform_prefix = "INS_"
    elif platform.upper() == "YOUTUBE":
        platform_prefix = "TY_"

    try:
        for item in raw_data:
            std_item = {
                "sec_uid": "",
                "kol_id": "",
                "kol_name": "",
                "username": "",
                "url": "",
                "engagement_rate": "",
                "averageViews": ""
            }

            # 根据不同数据源进行标准化处理
            if source == "creable":
                if "account" in item and "user_profile" in item["account"]:
                    profile = item["account"]["user_profile"]
                    std_item["sec_uid"] = profile.get("sec_uid", "")
                    std_item["kol_id"] = f"{platform_prefix}{profile.get('username', '')}"
                    std_item["kol_name"] = profile.get("username", "")
                    std_item["username"] = profile.get("fullname", "")
                    std_item["url"] = profile.get("url", "")
                    std_item["engagement_rate"] = profile.get("engagement_rate", "")
                    std_item["averageViews"] = profile.get("avg_views", "")

            elif source == "modash":
                if "profileData" in item and "profile" in item["profileData"]:
                    profile_data = item["profileData"]
                    profile = profile_data.get("profile", {})
                    std_item["sec_uid"] = profile_data.get("secUid", "")
                    std_item["kol_id"] = f"{platform_prefix}{profile.get('username', '')}"
                    std_item["kol_name"] = profile.get("username", "")
                    std_item["username"] = profile.get("fullname", "")
                    std_item["url"] = profile.get("url", "")
                    std_item["engagement_rate"] = profile.get("engagementRate", "")
                    std_item["averageViews"] = profile.get("averageViews", "")

            elif source == "ttone":
                if "creatorTTInfo" in item and "statisticData" in item:
                    creator_info = item["creatorTTInfo"]
                    stats = item.get("statisticData", {}).get("overallPerformance", {})

                    handle_name = creator_info.get("handleName", "")
                    std_item["sec_uid"] = ""  # TTONE没有sec_uid
                    std_item["kol_id"] = f"{platform_prefix}{handle_name}"
                    std_item["kol_name"] = handle_name
                    std_item["username"] = creator_info.get("nickName", "")
                    std_item["url"] = f"https://www.tiktok.com/@{handle_name}" if handle_name else ""
                    std_item["engagement_rate"] = stats.get("engagementRate", "")
                    std_item["averageViews"] = stats.get("medianViews", "")

            if std_item["kol_id"]:  # 确保至少有KOL ID
                standardized_list.append(std_item)

        logger.info(f"数据标准化处理完成，共处理 {len(standardized_list)} 条数据")
        return standardized_list

    except Exception as e:
        logger.exception(f"数据标准化处理时发生错误: {str(e)}")
        return []


def _collect_platform_data(platform: str, standardized_data: List[Dict], query_params: Dict) -> List[Dict]:
    """
    根据平台采集详细数据

    Args:
        platform: 平台名称（TikTok/Instagram/YouTube）
        standardized_data: 标准化后的数据列表
        query_params: 查询参数

    Returns:
        采集到的平台详细数据
    """
    logger.info(f"开始从平台 {platform} 采集详细数据")

    try:
        enriched_data = []

        if platform.lower() == "tiktok":
            # 对每个KOL的数据进行丰富
            for item in standardized_data:
                try:
                    # 通过URL获取TikTok详细数据
                    url = item.get("url", "")
                    if not url:
                        # 如果没有URL但有用户名，构建URL
                        username = item.get("kol_name", "")
                        if username:
                            url = f"https://www.tiktok.com/@{username}"
                        else:
                            logger.warning(f"无法为KOL构建TikTok URL: {item}")
                            enriched_data.append(item)
                            continue

                    # 使用TikTokAPIClient获取数据
                    logger.info(f"正在获取TikTok数据：{url}")
                    with TikTokAPIClient(url=url) as client:
                        user_info, videos = client.scrape_sync(sec_user_id=item.get("sec_uid", ""))

                        # 合并数据
                        item.update({
                            "platform": "tiktok",
                            "followers_count": user_info.get("followers_count", 0),
                            "likes_count": user_info.get("likes_count", 0),
                            "bio": user_info.get("bio", ""),
                            "video_count": len(videos),
                            "videos": videos
                        })

                    enriched_data.append(item)

                except Exception as e:
                    logger.error(f"获取TikTok KOL数据失败: {str(e)}, KOL: {item}")
                    # 即使失败也保留原始数据
                    item["platform"] = "tiktok"
                    enriched_data.append(item)

            return enriched_data

        elif platform.lower() == "instagram":
            # 对每个KOL的数据进行丰富
            for item in standardized_data:
                try:
                    # 获取Instagram用户名
                    username = item.get("kol_name", "")
                    if not username:
                        logger.warning(f"无法为KOL构建Instagram用户名: {item}")
                        enriched_data.append(item)
                        continue

                    # 使用InstagramAPIClient获取数据
                    logger.info(f"正在获取Instagram数据：{username}")
                    from app.services.instagram_crawler_service import InstagramAPIClient
                    with InstagramAPIClient(username) as scraper:
                        user_info, reels = scraper.scrape_sync(max_reels=20)

                        # 合并数据，字段名与user_info结构完全对齐
                        item.update({
                            "platform": "instagram",
                            "kol_id": user_info.get("kol_id", ""),  # 真实姓名
                            "username": user_info.get("username", ""),  # Instagram用户名
                            "followers_count": user_info.get("followers_count", 0),  # 粉丝数
                            "bio": user_info.get("bio", ""),  # 简介
                            "video_count": len(reels),
                            "videos": reels[:15]  # 只保留前15个Reels数据
                        })

                    enriched_data.append(item)

                except Exception as e:
                    logger.error(f"获取Instagram KOL数据失败: {str(e)}, KOL: {item}")
                    # 即使失败也保留原始数据
                    item["platform"] = "instagram"
                    enriched_data.append(item)

            return enriched_data

        elif platform.lower() == "youtube":
            # YouTube平台数据采集暂未实现
            logger.warning("YouTube平台数据采集暂未实现")
            # 添加平台标识后返回
            for item in standardized_data:
                item["platform"] = "youtube"
            return standardized_data

        else:
            logger.error(f"不支持的平台: {platform}")
            return []

    except Exception as e:
        logger.exception(f"从平台 {platform} 采集详细数据时发生错误: {str(e)}")
        return []


def _store_data_to_db(db: Session, platform_data: List[Dict], query_params: Optional[Dict] = None) -> List[Dict]:
    """
    将数据存储到数据库

    Args:
        db: 数据库会话
        platform_data: 平台数据
        query_params: 查询参数，包含filter_name，filter_body，project_code等

    Returns:
        存储后的数据列表
    """
    logger.info("开始将数据存储到数据库")

    stored_data = []
    filter_id = None
    project_code = None

    # 提取query_params中的参数
    if query_params:
        project_code = query_params.get("project_code", "")
        filter_name = query_params.get("filter_name", "")
        filter_body = query_params.get("filter_body", "{}")

    # 检查并转换filter_body
    if isinstance(filter_body, str):
        try:
            filter_body = json.loads(filter_body)
        except Exception as e:
            logger.error(f"解析filter_body失败: {str(e)}")
            filter_body = {}

    try:
        # 筛选条件处理
        filter_data_obj = None
        if filter_name and project_code and filter_body:
            # 有 filter_name 和 project_code，查找或创建筛选条件
            filter_data_obj = filter_data.get_by_name_and_project_code_and_body(db,
                                                                                name=filter_name,
                                                                                project_code=project_code,
                                                                                filter_body=filter_body
                                                                                )
            if not filter_data_obj:
                # 创建新的筛选条件
                filter_obj_in = {
                    "filter_name": filter_name,
                    "project_code": project_code,
                    "filter_body": filter_body
                }
                filter_data_obj = filter_data.create(db, obj_in=filter_obj_in)
                logger.info(f"创建新 filter_data: {filter_name}, project_code: {project_code}")
                filter_id = filter_data_obj.id

        # 遍历平台数据
        for item in platform_data:
            # 获取平台前缀，前缀为 TK_ / INS_ / TY_ 分别代表 TikTok / Instagram / YouTube
            if item.get("platform", "").lower() == "tiktok":
                prefix = "TK_"
            elif item.get("platform", "").lower() == "instagram":
                prefix = "INS_"
            elif item.get("platform", "").lower() == "youtube":
                prefix = "TY_"
            else:
                prefix = "OTHER_"

            # 让大模型提取 邮箱 和 keyword ai
            keywords_ai, email = email_and_keywords_from_bio(item.get("bio", ""))

            # 根据粉丝数判断等级
            followers_count = item.get("followers_count", 0)
            level = None
            if followers_count is not None:
                if followers_count < 10000:
                    level = "Nano 1k～10k"
                elif 10000 <= followers_count < 50000:
                    level = "Micro 10k～50k"
                elif followers_count >= 50000:
                    level = "Mid-tier 50k～500k"

            # 计算平均播放数和中位数
            mean_views_k, median_views_k = compute_view_mean_and_median_k(videos=item.get("videos", []))

            # 构建KOL信息对象
            kol_data = {
                "kol_id": f"{prefix}{item.get('kol_id', '')}",
                "kol_name": item.get("kol_name", ""),
                "username": item.get("username", ""),
                "email": email,
                "bio": item.get("bio", ""),
                "account_link": item.get("url", ""),  # 使用url作为账号链接
                "platform": item.get("platform", ""),
                "engagement_rate": item.get("engagement_rate", 0),
                "average_views_k": item.get("averageViews", 0) / 1000 if item.get("averageViews") else 0,  # 转换为K单位
                "followers_k": item.get("followers_count", 0) / 1000 if item.get("followers_count") else 0,  # 转换为K单位
                "likes_k": item.get("likes_count", 0) / 1000 if item.get("likes_count") else 0,  # 转换为K单位
                "keywords_ai": keywords_ai,
                "source": query_params.get("source", ""),
                "level": level,
                "mean_views_k": mean_views_k,
                "median_views_k": median_views_k
            }

            # 检查KOL ID是否有效
            if not kol_data["kol_id"]:
                logger.warning(f"无效的KOL ID，跳过此KOL: {kol_data}")
                continue

            # 检查KOL是否已存在，存在则更新，不存在则创建
            existing_kol = kol_info.get(db, id=kol_data["kol_id"])
            if existing_kol:
                # 更新现有KOL信息
                stored_kol = kol_info.update(db, db_obj=existing_kol, obj_in=kol_data)
                logger.info(f"更新KOL信息: {kol_data['kol_id']}")
            else:
                # 创建新的KOL信息
                stored_kol = kol_info.create(db, obj_in=kol_data)
                logger.info(f"创建新KOL信息: {kol_data['kol_id']}")

            # 创建KOL和筛选条件的关联关系
            if filter_id and filter_data_obj:
                association_data = {
                    "filter_id": filter_id,
                    "kol_id": kol_data["kol_id"],
                    "project_code": project_code or filter_data_obj.project_code
                }

                # 检查关联是否已存在
                existing_association = filter_kol_association.get_by_filter_id_and_kol_id(
                    db, filter_id=filter_id, kol_id=kol_data["kol_id"]
                )

                if not existing_association:
                    # 创建新的关联关系
                    filter_kol_association.create(db, obj_in=association_data)
                    logger.info(f"创建 KOL 和 filter_data 的关联关系: {kol_data['kol_id']} ---> {filter_id}")

            # 处理视频数据
            if item.get("videos"):
                videos = item["videos"]
                for video in videos:
                    video_data = {
                        "video_id": video.get('video_id', ""),
                        "kol_id": kol_data["kol_id"],
                        "play_count": video.get("play_count", 0),
                        "is_pinned": video.get("is_pinned", None),
                        "share_url": video.get("share_url", ""),
                        "desc": video.get("desc", ""),
                        "desc_language": video.get("desc_language", ""),
                        "video_url": video.get("video_url", ""),
                        "music_url": video.get("music_url", ""),
                        "likes_count": video.get("likes_count", 0),
                        "comments_count": video.get("comments_count", 0),
                        "shares_count": video.get("shares_count", 0),
                        "collect_count": video.get("collect_count", 0),
                        "create_time": video.get("create_time"),
                        "platform": item.get("platform", ""),
                        "hashtags": video.get("hashtags", [])
                    }

                    # 检查视频ID是否有效
                    if not video_data["video_id"]:
                        continue

                    # 检查视频是否已存在
                    existing_video = video_info.get(db, id=video_data["video_id"])
                    if not existing_video:
                        # 创建新的视频信息（对视频信息不做更新操作）
                        video_info.create(db, obj_in=video_data)
                        logger.info(f"创建 video_data: {video_data['video_id']}")

            # 添加到返回结果
            stored_data.append({**kol_data, "db_id": stored_kol.kol_id})

        logger.info(f"成功将 {len(stored_data)} 条数据存储到数据库")
        return stored_data

    except Exception as e:
        logger.exception(f"将数据存储到数据库时发生错误: {str(e)}")
        return []


def _send_to_feishu(data: List[Dict], query_params: Optional[Dict] = None, hp_records: Optional[List[Dict]] = None) -> \
Tuple[bool, int]:
    """
    将数据发送到飞书邮箱表

    Args:
        data: 数据列表
        query_params: 查询参数
        hp_records: 用于去重的高精表

    Returns:
        是否成功发送
    """
    logger.info(f"<Email>开始将 {len(data)} 条数据写入到<自动发邮件>飞书表")

    try:
        # 初始化Lark客户端
        lark_client = Lark()

        # 获取飞书配置
        save_node_token = settings.FEISHU_EMAIL_APP_TOKEN
        table_id = settings.FEISHU_EMAIL_TABLE_ID

        app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        if not app_token:
            logger.error(f"查询飞书 app_token 失败; \n{table_id}\n{save_node_token}")
            return False, 0
        else:
            app_token = app_token.get("obj_token")

        if not save_node_token or not table_id:
            logger.error("缺少必要的飞书配置: FEISHU_EMAIL_APP_TOKEN 或 FEISHU_EMAIL_TABLE_ID")
            return False, 0

        # 格式化数据为飞书表格所需格式
        records = []
        for item in data:
            if item.get("email"):
                records.append({
                    "KOL ID": item.get("kol_name", ""),
                    "Email": item.get("email", ""),
                    "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
                    "KOL Name": item.get("username", ""),
                    "Template": query_params.get("template", ""),
                    "App Code": query_params.get("project_code", ""),
                })

        # 获取所有现有记录的KOL ID
        existing_records = lark_client.search_table_all_records(app_token, table_id, {})
        existing_kol_ids = {record.get("fields", {}).get("KOL ID", [])[0]["text"] for record in existing_records}

        # 过滤掉已存在的记录
        new_records = [record for record in records if record.get("KOL ID") not in existing_kol_ids]
        logger.debug(f"过滤后的记录数: {len(new_records)}")
        # logger.debug(f"过滤后的记录数: {new_records}")
        if not new_records:
            logger.warning("没有新数据需要存储到<自动发邮件>飞书表")
            return True, 0

        # 只保留同时存在于 hp_records 中的 KOL ID（取交集）
        if hp_records:
            hp_kol_ids = {record.get("KOL ID", "") for record in hp_records}
            new_records = [record for record in new_records if record.get("KOL ID") in hp_kol_ids]
            logger.debug(f"与高潜力 KOL 记录取交集后的记录数: {len(new_records)}")
        else:
            logger.debug(f"没有高潜力 KOL 记录，取交集依然为空")
            return True, 0

        # 批量创建记录
        result = lark_client.batch_create_table_records(app_token, table_id, new_records)

        if result:
            logger.info(f"成功将 {len(new_records)} 条新数据发送到<自动发邮件>飞书表")
            return True, len(new_records)
        else:
            logger.error("发送数据到<自动发邮件>飞书表失败")
            return False, 0

    except Exception as e:
        logger.exception(f"发送数据到<自动发邮件>飞书表时发生错误: {str(e)}")
        return False, 0


def _store_high_potential_data(filter_name: str, project_code: str, data: List[Dict], table_save: str,
                               table_duplicate: str) -> Tuple[bool, list]:
    """
    将高潜力KOL数据存储到指定飞书表

    Args:
        data: 数据列表
        table_save: 存储表ID或URL
        table_duplicate: 去重表ID或URL列表，以逗号分隔

    Returns:
        是否成功存储
    """
    logger.info(f"<HP>开始将 {len(data)} 条高潜力KOL数据存储到<高潜力>飞书表")

    try:
        # 初始化Lark客户端
        lark_client = Lark()

        # 解析表格URL参数
        save_node_token, save_table_id = _parse_table_url(table_save)
        app_token = lark_client.get_wiki_node_info(node_token=save_node_token)
        if not app_token:
            logger.error(f"查询飞书 app_token 失败; \n{table_save}\n{save_node_token}")
            return False, []
        else:
            app_token = app_token.get("obj_token")

        if not save_table_id:
            logger.error(f"解析目标存储表URL失败: {table_save}")
            return False, []

        # 处理查重表格列表
        duplicate_tables = []
        if table_duplicate:
            duplicate_tables = table_duplicate.split(',')

        if table_save:
            # 往 duplicate_tables 中添加目标存储表
            duplicate_tables.append(table_save)

        # 查询所有表格中的现有数据，并合并KOL ID列表
        existing_kol_ids = set()
        for dup_table in duplicate_tables:
            dup_node_token, dup_table_id = _parse_table_url(dup_table)
            if not dup_table_id:
                logger.warning(f"解析查重表URL失败，将跳过此表: {dup_table}")
                continue

            # 获取表格中的所有记录
            duplicate_records = lark_client.search_table_all_records(app_token, dup_table_id, {}, page_size=500)

            # 提取KOL ID并添加到集合中
            for record in duplicate_records:
                kol_id_field = record.get("fields", {}).get("KOL ID", [])
                if kol_id_field:
                    if isinstance(kol_id_field, list):
                        kol_id = kol_id_field[0].get("text", "") if kol_id_field else ""
                    else:
                        kol_id = kol_id_field
                    if kol_id:
                        existing_kol_ids.add(kol_id)

        # 构建记录数据，转换为飞书多维表格格式
        records = []
        for item in data:
            # 如果KOL ID已存在于任何查重表中，则跳过
            if item.get("kol_name") in existing_kol_ids:
                continue

            record = {
                "KOL ID": item.get("kol_name", ""),  # 写入飞书表格不能有 TK_ 前缀
                "Email": item.get("email", ""),
                "Account link": {"text": item.get("account_link", ""), "link": item.get("account_link", "")},
                "KOL Name": item.get("username", ""),
                "Bio": item.get("bio", ""),
                "Source": item.get("source", ""),
                "Filter": filter_name,
                "Followers(K)": item.get('followers_k', 0),
                "Mean Views(K)": item.get('mean_views_k', 0),
                "Median Views(K)": item.get('median_views_k', 0),
                "Keywords-AI": ", ".join(str(keyword) for keyword in item.get('keywords_ai', []) if keyword),
                "Level": item.get('level'),
                "Project": project_code,
                "Engagement Rate(%)": f"{item.get('engagement_rate', 0) * 100:.2f} %",
                "Average Views(K)": item.get('average_views_k', 0),
            }
            records.append(record)
        if not records:
            logger.info("没有新数据需要存储到<高潜力>飞书表")
            return True, []

        # 批量创建记录
        result = lark_client.batch_create_table_records(app_token, save_table_id, records)

        if result:
            logger.info(f"成功将 {len(records)} 条新数据存储到<高潜力>飞书表")
            return True, records
        else:
            logger.error(f"存储高潜力KOL数据到<高潜力>飞书表失败")
            return False, []

    except Exception as e:
        logger.exception(f"存储高潜力KOL数据到<高潜力>飞书表时发生错误: {str(e)}")
        return False, []


def _parse_table_url(table_url: str) -> Tuple[str, str]:
    """
    解析飞书表格URL，提取node_token和table_id

    Args:
        table_url: 飞书表格URL或ID

    Returns:
        Tuple[str, str]: (node_token, table_id)
    """
    try:
        # 如果直接是table_id，直接返回
        if table_url.startswith("tbl"):
            return "", table_url

        # 解析URL格式
        import re
        # 提取node_token
        node_token_match = re.search(r"wiki/([^/?]+)", table_url)
        node_token = node_token_match.group(1) if node_token_match else ""

        # 提取table_id
        table_id_match = re.search(r"table=([^&]+)", table_url)
        table_id = table_id_match.group(1) if table_id_match else ""

        return node_token, table_id

    except Exception as e:
        logger.error(f"解析表格URL时发生错误: {str(e)}")
        return "", ""